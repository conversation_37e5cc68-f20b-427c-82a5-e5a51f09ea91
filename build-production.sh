#!/bin/bash

# Load environment variables from .env.production file
if [ -f .env.production ]; then
  export $(grep -v '^#' .env.production | xargs)
fi

# Now build your Docker image, passing the loaded environment variables as build-args
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/frontend_agentq:1.1.0 \
  --build-arg VITE_CORE_SERVICE_URL=https://backend-core-api.agentq.id \
  --build-arg VITE_APP_URL=https://app.agentq.id \
  --push .



# docker buildx build \
#   --platform linux/amd64,linux/arm64 \
#   --build-arg VITE_CORE_SERVICE_URL="http://localhost:3000" \
#   --build-arg VITE_APP_URL="http://localhost:5174" \
#   -t frontend_agentq:latest \
#   .
