<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <a href="https://agentq.id" class="text-2xl font-bold text-e94560 ">AgentQ</a>
          <div class="flex items-center space-x-4">
            <!-- Show Go to App button only for Enterprise users -->
            <button 
               
              @click="goToApp" 
              class="header-button"
            >
              <span class="material-icons mr-1">launch</span>
              Go to App
            </button>
            <!-- Settings button -->
            <button 
              @click="goToSettings" 
              class="header-button"
            >
              <span class="material-icons mr-1">settings</span>
              Settings
            </button>
            <button @click="handleLogout" class="header-button">
              <span class="material-icons mr-1">logout</span>
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- API Key Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">API Key</h2>
          <button @click="copyApiKey" class="text-button">
            <span class="material-icons mr-1">content_copy</span>
            Copy
          </button>
        </div>
        <div class="bg-gray-50 p-4 rounded-md font-mono text-sm break-all">
          {{ profileData?.company?.token || 'Loading API key...' }}
        </div>
      </div>

      <!-- Current Plan Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-xl font-semibold text-gray-900">Current Plan</h2>
            <div class="mt-2">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                {{ profileData?.company?.subscription?.name || 'Loading plan name...' }}
              </span>
            </div>
            <div class="mt-2">
              <span class="text-sm text-gray-500">You can used up to <strong> {{ profileData?.company?.subscription?.tokenLimit || '0...' }} </strong> tokens per month</span>
            </div>
            <div class="mt-2">
              <span class="text-sm text-gray-500" v-if="profileData?.company?.subscription?.isEnterprise">
                <strong> {{ profileData?.company?.subscription?.remainingTokens }} </strong> tokens left. 
                <strong> {{ profileData?.company?.subscription?.tokenLimit }} </strong> tokens will be added in {{ profileData?.company?.subscription?.daysUntilReset }} days.
              </span>
              <span class="text-sm text-gray-500" v-else>
                <strong> {{ profileData?.company?.subscription?.remainingTokens }} </strong> tokens left. 
                Reset to <strong> {{ profileData?.company?.subscription?.tokenLimit || '0...' }} </strong> in {{ profileData?.company?.subscription?.daysUntilReset }} days.
              </span>
            </div>
          </div>
          <button @click="showUpgradeModal = true" class="primary-button">
            Upgrade & Top Up Token
          </button>
        </div>
      </div>

      <!-- Monthly Usage Section -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Monthly Usage</h2>
        
        <!-- Usage Chart -->
        <div class="h-64 mb-6">
          <canvas ref="usageChart"></canvas>
        </div>

        <!-- Usage Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="stat-card">
            <span class="material-icons text-blue-500 text-2xl mb-2">calendar_today</span>
            <h3 class="text-sm font-medium text-gray-500">Current Period</h3>
            <p class="text-2xl font-semibold text-gray-900">{{ currentPeriod }}</p>
          </div>
          
          <div class="stat-card">
            <span class="material-icons text-green-500 text-2xl mb-2">token</span>
            <h3 class="text-sm font-medium text-gray-500">Total Tokens Used</h3>
            <p class="text-2xl font-semibold text-gray-900">{{ totalTokens.toLocaleString() }}</p>
          </div>
          
          <div class="stat-card">
            <span class="material-icons text-red-500 text-2xl mb-2">event_busy</span>
            <h3 class="text-sm font-medium text-gray-500">Plan Expires</h3>
            <p class="text-2xl font-semibold text-gray-900">
              {{ profileData?.company?.subscription?.endDate ? new Date(profileData?.company?.subscription?.endDate).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) : 'Never' }}
            </p>
          </div>
        </div>
      </div>

      <!-- Payment History Section -->
      <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">Payment History</h2>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="loadingPayments">
                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">Loading payment history...</td>
              </tr>
              <tr v-else-if="recentPayments.length === 0">
                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">No payment history found</td>
              </tr>
              <tr v-for="payment in recentPayments" :key="payment.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(payment.created_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  Rp {{ formatAmount(payment.amount) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(payment.status)">
                    {{ formatStatus(payment.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a 
                    v-if="payment.status === 'settlement' || payment.status === 'capture' || payment.status === 'paid'" 
                    @click.prevent="viewInvoiceDetails(payment.order_id)"
                    href="#"
                    class="text-green-600 hover:text-green-900 cursor-pointer"
                  >
                    View
                  </a>
                  <span v-else-if="payment.status === 'pending'">
                    <button 
                      @click="viewInvoiceDetails(payment.order_id)"
                      class="text-green-600 hover:text-green-900 mr-3"
                    >
                      View
                    </button>
                    <button 
                      @click="checkPaymentStatus(payment.order_id)"
                      class="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      Check Status
                    </button>
                    <button 
                      @click="openPaymentPage(payment.order_id)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      Pay Now
                    </button>
                  </span>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Pagination Controls -->
        <div class="flex justify-between items-center mt-6 text-sm" v-if="totalPages > 0">
          <div class="text-gray-500">
            Page {{ currentPage }} of {{ totalPages }}
          </div>
          <div class="flex space-x-2">
            <button 
              @click="loadPage(1)" 
              :disabled="currentPage === 1"
              class="pagination-button"
              :class="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''"
            >
              <span class="material-icons text-sm">first_page</span>
            </button>
            <button 
              @click="loadPage(currentPage - 1)" 
              :disabled="currentPage === 1"
              class="pagination-button"
              :class="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''"
            >
              <span class="material-icons text-sm">chevron_left</span>
            </button>
            
            <!-- Page Numbers -->
            <div class="flex space-x-1">
              <template v-for="page in displayedPages" :key="page">
                <button 
                  v-if="page !== '...'"
                  @click="typeof page === 'number' && loadPage(page)" 
                  class="w-8 h-8 rounded-full flex items-center justify-center"
                  :class="currentPage === page ? 'bg-e94560 text-white' : 'text-gray-700 hover:bg-gray-100'"
                >
                  {{ page }}
                </button>
                <span v-else class="w-8 h-8 flex items-center justify-center text-gray-500">...</span>
              </template>
            </div>
            
            <button 
              @click="loadPage(currentPage + 1)" 
              :disabled="currentPage === totalPages"
              class="pagination-button"
              :class="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''"
            >
              <span class="material-icons text-sm">chevron_right</span>
            </button>
            <button 
              @click="loadPage(totalPages)" 
              :disabled="currentPage === totalPages"
              class="pagination-button"
              :class="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''"
            >
              <span class="material-icons text-sm">last_page</span>
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>

  <UpgradePlanModal 
    v-if="showUpgradeModal" 
    :currentSubscription="profileData?.company?.subscription" 
    @close="showUpgradeModal = false"
    @upgrade="handleUpgradeSuccess"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import Chart from 'chart.js/auto';
import UpgradePlanModal from '../components/common/UpgradePlanModal.vue';

const router = useRouter();
const authStore = useAuthStore();
const usageChart = ref<HTMLCanvasElement | null>(null);
const profileData = ref<any>(null);
const totalTokens = ref(0);

const currentPeriod = new Date().toLocaleDateString('en-US', { 
  year: 'numeric', 
  month: 'long'
});

const handleLogout = () => {
  authStore.logout();
  router.push('/login');
};

const copyApiKey = async () => {
  if (profileData.value?.company?.token) {
    try {
      await navigator.clipboard.writeText(profileData.value.company.token);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy API key:', err);
    }
  }
};

const showUpgradeModal = ref(false);

const handleUpgradeSuccess = () => {
  showUpgradeModal.value = false;
};

const fetchProfileData = async () => {
  try {
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });
    
    if (!response.ok) {
        if(response.status === 401){
            localStorage.removeItem('token');
            router.push('/login');
            return;
        }
      throw new Error('Failed to fetch profile data');
    }

    const data = await response.json();
    profileData.value = data;
  } catch (error) {
    console.error('Error fetching profile data:', error);
    localStorage.removeItem('token');
    router.push('/login');
  }
};

const fetchUsageData = async () => {
  try {
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile/usage`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch usage data');
    }

    const data = await response.json();
    
    // Calculate total tokens
    totalTokens.value = data.reduce((acc: number, curr: any) => acc + curr.tokenUsed, 0);

    // Group data by year and month
    const monthlyData: { [key: string]: number } = {};
    data.forEach((item: any) => {
      const date = new Date(item.timestamp);
      const year = date.getFullYear();
      const month = date.getMonth();
      const key = `${year}-${month + 1}`; // Use 'year-month' as key
      monthlyData[key] = (monthlyData[key] || 0) + item.tokenUsed;
    });

    // Get current year and month
    const currentDate = new Date();
    let currentYear = currentDate.getFullYear();
    let currentMonth = currentDate.getMonth() + 1; // Months are 0-indexed

    // Generate the last 12 months based on the current month
    const last12Months: string[] = [];
    for (let i = 0; i < 12; i++) {
      last12Months.unshift(`${currentYear}-${currentMonth}`);
      currentMonth--;
      if (currentMonth === 0) {
        currentMonth = 12;
        currentYear--;
      }
    }

    // Fill missing data
    const filledData: number[] = last12Months.map(month => monthlyData[month] || 0);

    // Update chart
    if (usageChart.value) {
      const ctx = usageChart.value.getContext('2d');
      if (ctx) {
        new Chart(ctx, {
          type: 'line',
          data: {
            labels: last12Months.map(month => {
              const [year, monthNum] = month.split('-');
              return new Date(Number(year), Number(monthNum) - 1).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
              });
            }), // Convert 'year-month' to 'MMM YYYY'
            datasets: [{
              label: 'Token Usage',
              data: filledData,
              borderColor: '#E94560',
              tension: 0.4,
              fill: false,
              pointRadius: 5,
              pointHoverRadius: 7
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  color: '#f3f4f6'
                }
              },
              x: {
                grid: {
                  color: '#f3f4f6'
                }
              }
            },
            plugins: {
              legend: {
                display: false
              }
            }
          }
        });
      }
    }
  } catch (error) {
    console.error('Error fetching usage data:', error);
  }
};

const recentPayments = ref<any[]>([]);
const loadingPayments = ref(true);
let checkInterval: number | null = null;

const currentPage = ref(1);
const totalPages = ref(1);
const pageSize = 5; // Number of items per page

const displayedPages = computed(() => {
  const pages = [];
  const maxVisiblePages = 5;
  
  if (totalPages.value <= maxVisiblePages) {
    // If we have fewer pages than the max, show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);
    
    // Calculate start and end of visible pages
    let start = Math.max(2, currentPage.value - 1);
    let end = Math.min(totalPages.value - 1, currentPage.value + 1);
    
    // Adjust if we're at the beginning or end
    if (currentPage.value <= 2) {
      end = Math.min(totalPages.value - 1, 4);
    } else if (currentPage.value >= totalPages.value - 1) {
      start = Math.max(2, totalPages.value - 3);
    }
    
    // Add ellipsis if needed
    if (start > 2) {
      pages.push('...');
    }
    
    // Add middle pages
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    // Add ellipsis if needed
    if (end < totalPages.value - 1) {
      pages.push('...');
    }
    
    // Always show last page
    pages.push(totalPages.value);
  }
  
  return pages;
});

onMounted(async () => {
  await fetchProfileData();
  await fetchUsageData();
  await fetchRecentPayments();
  
  // Check for pending payments every 60 seconds
  checkInterval = window.setInterval(() => {
    checkPendingPayments();
  }, 60000);
});

onUnmounted(() => {
  if (checkInterval) {
    clearInterval(checkInterval);
  }
});

const fetchRecentPayments = async () => {
  try {
    loadingPayments.value = true;
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/history?page=${currentPage.value}&limit=${pageSize}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch payment history');
    }

    const data = await response.json();
    recentPayments.value = data.items;
    totalPages.value = data.meta.totalPages;
    loadingPayments.value = false;
  } catch (error) {
    console.error('Error fetching payment history:', error);
    loadingPayments.value = false;
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric'
  });
};

const formatAmount = (amount: number | string): string => {
  // Ensure amount is treated as a number
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  // Format with thousands separator and 2 decimal places
  return numAmount.toLocaleString('id-ID', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  });
};

const formatStatus = (status: string) => {
  switch (status.toLowerCase()) {
    case 'settlement':
    case 'capture':
      return 'Paid';
    case 'refund':
      return 'Refunded';
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'Failed';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1);
  }
};

const getStatusClass = (status: string) => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'settlement':
    case 'capture':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800';
    case 'pending':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800';
    case 'failed':
    case 'deny':
    case 'cancel':
    case 'expire':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800';
    case 'refund':
    case 'refunded':
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800';
    default:
      return 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800';
  }
};

const checkPaymentStatus = async (orderId: string) => {
  try {
    loadingPayments.value = true;
    const response = await fetch(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/payments/check-status/${orderId}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to check payment status');
    }

    const data = await response.json();
    
    if (data.status === 'success') {
      // Refresh profile data to get updated subscription info
      await fetchProfileData();
      // Reload payments to show updated status
      await fetchRecentPayments();
    }
    
    loadingPayments.value = false;
  } catch (error) {
    console.error('Error checking payment status:', error);
    loadingPayments.value = false;
  }
};

const openPaymentPage = (orderId: string) => {
  router.push(`/payment/resume?order_id=${orderId}`);
};

const checkPendingPayments = async () => {
  try {
    // Find all pending payments
    const pendingPayments = recentPayments.value.filter(p => 
      p.status.toLowerCase() === 'pending'
    );
    
    // Check status for each pending payment
    for (const payment of pendingPayments) {
      await checkPaymentStatus(payment.order_id);
    }
  } catch (error) {
    console.error('Error checking pending payments:', error);
  }
};

const viewInvoiceDetails = (orderId: string) => {
  router.push(`/invoice/${orderId}`);
};

const goToApp = () => {
  // Pass the current auth token and company ID to the app URL
  const token = authStore.token;
  const companyId = profileData.value?.company?.id;
  const companyName = profileData.value?.company?.name;
  const companyUser = profileData.value?.company?.users[0];
  const companySubscription = profileData.value?.company?.subscription;
  
  // Encode company data to safely include in URL
  const companyData = encodeURIComponent(JSON.stringify({
    id: companyId,
    name: companyName,
    user: companyUser ? {
      id: companyUser.id,
      name: companyUser.name,
      email: companyUser.email,
      role: companyUser.role
    } : null,
    subscription: companySubscription ? {
      id: companySubscription.id,
      name: companySubscription.name,
      isEnterprise: companySubscription.isEnterprise
    } : null
  }));
  
  window.location.href = `${(import.meta as any).env.VITE_APP_URL || 'https://app.agentq.id'}/auth/external?token=${token}&company=${companyData}`;
};

const goToSettings = () => {
  router.push('/settings');
};

const loadPage = (page: number) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
  fetchRecentPayments();
};
</script>

<style lang="scss" scoped>
.header-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  border-radius: 0.375rem;
  transition: all 0.2s;

  &:hover {
    background-color: #f3f4f6;
    color: #111827;
  }
}

.text-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #e94560;
  border-radius: 0.375rem;
  transition: all 0.2s;

  &:hover {
    background-color: #fee2e7;
  }
}

.primary-button {
  background-color: #e94560;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  transition: all 0.2s;

  &:hover {
    background-color: #d63553;
  }
}

.stat-card {
  background-color: #f9fafb;
  padding: 2rem;
  border-radius: 0.5rem;
  text-align: center;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-4px);
  }

  h3 {
    font-size: 1rem;
  }
}

.text-e94560 {
  color: #e94560;
}

.bg-e94560 {
  background-color: #e94560;
}

.copied-message {
  color: #28a745;
  font-weight: bold;
  margin-left: 0.5rem;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: all 0.2s;
  
  &:hover:not(:disabled) {
    background-color: #f3f4f6;
  }
}
</style>
